import {
  Language,
  LanguageProficiency,
  Password,
  Role,
  User,
  UserLanguage,
  Waitlist,
  LaunchWaitlist,
  VideoCollection,
  VideoCollectionLikesMap,
  VideoCollectionProgress,
  VideoProgress,
  VideoLike,
  Transaction,
  Club,
  ClubPriceDetails,
  VideoCollectionMap,
  VideoCategory,
  Video,
} from "./mongo";
import { Types } from "mongoose";
import argon from "argon2";
import {
  CartType,
  ClassesPricingType,
  ClubType,
  PasswordType,
  SingleMembershipType,
  type UserType,
} from "./mongoTypes";
import {
  PasswordError,
  PasswordValidationError,
  UserExistsError,
  UserPasswordDoesNotMatchError,
} from "./errors";
import {
  emailValidator,
  waitlistValidator,
  launchWaitlistValidator,
} from "./validators";
import { ZodError } from "zod";
import { awsDownload } from "./aws";
import {
  CLASSESS_FETCH_DURATION,
  CURRENCY_ENUM,
  PAYMENT_STATUS,
  PROGRESS_STATUS,
} from "@/constant/Enums";
import {
  AMOUNT_CONVERTOR,
  getMondayAndThursday,
  isInPersonGroupClassLive,
} from "@/utils/classes";
import { stripe } from "./stripe";
import { isSameDay } from "date-fns";
import {
  dateAsPerTimeZone,
  getDateAsPerUTC,
  getEndOfTheDay,
} from "@/utils/dateTime";
import { localPlanType, Maybe } from "@/types";
import { addDays, startOfDay } from "date-fns";
import { zonedTimeToUtc, utcToZonedTime } from "date-fns-tz";

export const getUser = async (
  email: string,
  populate: boolean = false
): Promise<
  UserType | (Omit<UserType, "password"> & { password: PasswordType })
> => {
  try {
    const user = await User.findOne({ email });
    return populate
      ? await user.populate<{ password: PasswordType }>("password")
      : user;
  } catch (err) {
    console.error(err);
    throw new UserPasswordDoesNotMatchError();
  }
};

export const addToWaitlist = async (
  email: string,
  fname: string,
  lname: string,
  language: string
): Promise<boolean> => {
  try {
    const createValidated = await waitlistValidator.parseAsync({
      email,
      fname,
      lname,
      language,
    });
    let user = await Waitlist.findOne({ email });
    if (user) throw new UserExistsError();
    user = await Waitlist.create({ email, fname, lname, language });
    return true;
  } catch (err) {
    return false;
  }
};

type WaitlistResponse = {
  status: boolean;
  message: string;
};

export const addToLaunchWaitlist = async (
  email: string
): Promise<WaitlistResponse> => {
  try {
    const { email: validatedEmail } = await launchWaitlistValidator.parseAsync({
      email,
    });

    const existingUser = await LaunchWaitlist.findOne({
      email: validatedEmail,
    });
    if (existingUser) {
      console.error("User already exists:", validatedEmail);
      return { status: false, message: "User already exists" };
    }

    await LaunchWaitlist.create({ email: validatedEmail });
    return { status: true, message: "User successfully added to the waitlist" };
  } catch (err) {
    if (err instanceof ZodError) {
      console.error("Validation error:", err.errors);
      return { status: false, message: "Invalid email format" };
    }
    console.error("Unexpected error:", err);
    return { status: false, message: "An unexpected error occurred" };
  }
};

// export const createUser = async (
//   email: string,
//   password: string,
//   firstName: string,
//   lastName: string,
//   primaryLanguage: string,
//   proficiencies: Record<string, string>,
//   phone?: string,
//   roles: string[] = ["user"]
// ): Promise<UserType | null> => {
//   try {
//     await userCreateValidator.parseAsync({
//       email,
//       password,
//       confirmPassword: password,
//       firstName,
//       lastName,
//       primaryLanguage,
//       phone,
//     });
//     let user = await getUser(email, false);
//     if (user) throw new UserExistsError();
//     user = await User.create({
//       email,
//       firstName,
//       lastName,
//       phone,
//       languages: [],
//     });
//     for (let [l, p] of Object.entries(proficiencies)) {
//       console.warn({ l, p });
//       const lObj = Types.ObjectId.createFromHexString(l);
//       const pObj = Types.ObjectId.createFromHexString(p);
//       const ul = await UserLanguage.create({
//         user,
//         language: lObj,
//         proficiency: pObj,
//       });
//       console.info(user);
//       user.languages.push(ul._id);
//     }
//     user.markModified("languages");
//     const rs = await Role.find({});
//     const rolesToAdd = rs.filter((r) => roles.includes(r.name));
//     user.roles = rolesToAdd.map((r) => r._id);
//     await user.save();
//     try {
//       const passwordHash = await argon.hash(password);
//       const pwd = await Password.create({ user, passwordHash });
//       user.password = pwd._id;
//       await user.save();
//       return user;
//     } catch (err) {
//       console.error(err);
//       const pwdToDelete = user.password;
//       const ulsToDelete = user.languages;
//       await User.deleteOne({ _id: user._id });
//       await Password.deleteOne({ _id: pwdToDelete });
//       await UserLanguage.deleteMany({ _id: { $in: ulsToDelete } });
//       throw new PasswordError([], 400);
//     }
//   } catch (err) {
//     if (err instanceof ZodError) {
//       const pve = new PasswordValidationError();
//       for (let issue of err.issues) {
//         const feIdx = pve.fieldErrors.findIndex((fe) => {
//           fe.field === issue.path[0];
//         });
//         if (feIdx === -1) {
//           pve.fieldErrors.push({
//             field: issue.path[0] as string,
//             messages: [issue.message],
//           });
//         } else {
//           pve.fieldErrors[feIdx].messages.push(issue.message);
//         }
//       }
//       throw pve;
//     }
//     const user = await getUser(email, false);
//     if (user) {
//       const ulsToDelete = user.languages;
//       const passwordToDelete = user.password;
//       await User.deleteOne({ _id: user._id });
//       await UserLanguage.deleteMany({ _id: { $in: ulsToDelete } });
//       if (passwordToDelete) await Password.deleteOne({ _id: passwordToDelete });
//     }
//     throw err;
//   }
// };

export async function createUserFromClerkData(clerkData: {
  clerkId: string;
  email: string;
  firstName: string;
  lastName: string;
}): Promise<any> {
  const { clerkId, email, firstName, lastName } = clerkData;

  try {
    const emailVerified = true; // This should be checked before calling this function
    if (!emailVerified) {
      throw new Error("Email address not verified");
    }

    const existingUser = await User.findOne({ email });
    if (existingUser) {
      // throw new Error("User already exists");
      const updatedUser = await User.findOneAndUpdate(
        { email },
        {
          clerkId,
          firstName,
          lastName,
        },
        {
          new: true,
        }
      );
      return updatedUser;
    }

    const newUser = new User({
      clerkId,
      email,
      firstName,
      lastName,
    });

    await newUser.save();
    return newUser; // Return the created user
  } catch (error) {
    console.error("Error creating user from clerk data:", error);
    throw error; // Rethrow to handle it in the API route
  }
}

// This helper function assumes removing a user and related data on failed creation
async function cleanupFailedUserCreation(user: UserType): Promise<void> {
  const pwdToDelete = user.password;
  const ulsToDelete = user.languages;
  await User.deleteOne({ _id: user._id });
  if (pwdToDelete) await Password.deleteOne({ _id: pwdToDelete });
  await UserLanguage.deleteMany({ _id: { $in: ulsToDelete } });
}

type getAllCollectionDetailsProps = {
  data: any;
  userId: String;
  needCoverImages: boolean;
  needLikeStatus: boolean;
  needProgress: boolean;
};
export const getAllCollectionDetails = async ({
  data,
  needCoverImages,
  needLikeStatus,
  needProgress,
  userId,
}: getAllCollectionDetailsProps) => {
  let singleCollection = null;
  try {
    singleCollection = data.toObject();
  } catch (error) {
    singleCollection = data;
  }
  let coverImageUrl = "";
  let isLiked = false;
  let videoCollectionProgress = null;

  try {
    if (singleCollection) {
      if (needCoverImages) {
        if (singleCollection?.imageId && singleCollection?.imageKey) {
          const cover = await awsDownload(singleCollection?.imageKey);
          coverImageUrl = cover?.data ?? "";
        }
      }

      if (needLikeStatus) {
        const liked = await VideoCollectionLikesMap.findOne({
          likedBy: userId,
          collectionId: singleCollection._id,
        });
        isLiked = !!liked;
      }

      if (needProgress) {
        const vCollectionProgress = await VideoCollectionProgress.findOne({
          collectionId: singleCollection._id,
          userId,
        });
        videoCollectionProgress = vCollectionProgress;
      }

      return {
        ...singleCollection,
        isLiked,
        coverImageUrl,
        videoCollectionProgress,
      };
    } else {
      return {
        ...singleCollection,
        isLiked,
        coverImageUrl,
        videoCollectionProgress,
      };
    }
  } catch (error) {
    console.error(
      `Something went wrong in getAllCollectionDetails due to `,
      error
    );
    throw error;
  }
};

type getAllVideoDetailsProps = {
  data: any;
  userId: String;
  needCreator: boolean;
  needLikeStatus: boolean;
  needProgress: boolean;
  needThumbnail: boolean;
  needCategoryIds?: boolean;
  needCollectionIds?: boolean;
  needEnSubtitleUrl?: boolean;
  needVideoUrl?: boolean;
  needEsSubtitleUrl?: boolean;
};
export const getAllVideoDetails = async ({
  data,
  needCreator,
  needLikeStatus,
  needProgress,
  userId,
  needThumbnail,
  needCategoryIds = false,
  needCollectionIds = false,
  needEnSubtitleUrl = false,
  needVideoUrl = false,
  needEsSubtitleUrl = false,
}: getAllVideoDetailsProps) => {
  let isLiked = false;
  let progress = null;
  let creator = null;
  let video = null;
  let categoryIds = [];
  let collectionIds = [];
  let thumbnailUrl = null;
  let videoUrl = null;
  let enSubtitleUrl = null;
  let esSubtitleUrl = null;

  try {
    video = data?.toObject();
  } catch (error) {
    video = data;
  }

  try {
    if (needCreator) {
      const videoObject = video;
      if (
        videoObject?.creator &&
        typeof videoObject?.creator !== "string" &&
        "profileImageKey" in videoObject?.creator
      ) {
        const awsImgKey = videoObject.creator.profileImageKey;
        if (awsImgKey) {
          const { data: profileImageUrl } = await awsDownload(awsImgKey);
          creator = {
            ...videoObject.creator,
            profileImageUrl,
          };
        } else {
          creator = {
            ...videoObject.creator,
            profileImageUrl: "",
          };
        }
      } else {
        creator = {
          ...(videoObject?.creator ?? {}),
          profileImageUrl: "",
        };
      }
    }

    if (needProgress) {
      const videoProgress = await VideoProgress.findOne({
        videoId: video._id,
        userId,
      });
      progress = videoProgress;
    }

    if (needLikeStatus) {
      const liked = await VideoLike.findOne({
        likedBy: userId,
        videoId: video._id,
      });
      isLiked = !!liked;
    }

    if (needCategoryIds) {
      const videoCategories = await VideoCategory.find({
        videoId: video._id,
      }).select("categoryId");
      categoryIds = videoCategories.map((m) => m.categoryId);
    }

    if (needCollectionIds) {
      const videoCollections = await VideoCollectionMap.find({
        videoId: video._id,
      }).select("collectionId");
      collectionIds = videoCollections.map((m) => m.collectionId);
    }

    if (needThumbnail) {
      const thumbnail = await awsDownload(video.thumbnailKey);
      thumbnailUrl = thumbnail?.data ?? "";
    }

    if (needVideoUrl) {
      const videoFile = await awsDownload(video.videoKey);
      videoUrl = videoFile?.data ?? "";
    }

    if (needEnSubtitleUrl) {
      const enSubtitle = await awsDownload(video.en_subtitleKey);
      enSubtitleUrl = enSubtitle?.data ?? "";
    }

    if (needEsSubtitleUrl) {
      const esSubtitle = await awsDownload(video.es_subtitleKey);
      esSubtitleUrl = esSubtitle?.data ?? "";
    }

    return {
      ...data,
      isLiked,
      progress,
      creator,
      categoryIds,
      thumbnailUrl,
      collectionIds,
      videoUrl,
      enSubtitleUrl,
      esSubtitleUrl,
    };
  } catch (error) {
    console.error(`Something went wrong in getAllVideoDetails due to `, error);
    throw error;
  }
};

type getAllEventDetailsProps = {
  needEventImage: boolean;
  data: any;
};
export const getAllEventDetails = async ({
  needEventImage,
  data,
}: getAllEventDetailsProps) => {
  try {
    let images: ImageArrayType[] = [];
    try {
      data = data.toObject();
    } catch (error) {}
    if (needEventImage) {
      await Promise.all(
        data.imagesKeysAndIds.map(async (m) => {
          const eventImage = await awsDownload(m.key);
          const imgUrl = eventImage?.data ?? "";
          images.push({
            key: m.key,
            id: m.id,
            url: imgUrl,
          });
        })
      );
    }
    return {
      ...data,
      images,
    };
  } catch (error) {
    console.error(`Something went wrong in getAllEventDetails due to `, error);
    throw error;
  }
};

type ImageArrayType = {
  key: string;
  id: string;
  url: string;
};

type getAllClubDetailsProps = {
  needClubImage: boolean;
  needCreatorImage: boolean;
  data: ClubType;
};
export const getAllClubDetails = async ({
  data,
  needClubImage,
  needCreatorImage = false,
}: getAllClubDetailsProps) => {
  try {
    try {
      data = data.toObject();
    } catch (e) {}
    if (needCreatorImage) {
      const creatorArray = data.teachers.map((m) => m as UserType);
      await Promise.all(
        creatorArray.map(async (m) => {
          if (m?.profileImageKey) {
            const profileImage = await awsDownload(m?.profileImageKey);
            m.profileImageUrl = profileImage?.data ?? "";
          } else {
            m.profileImageUrl = "";
          }
        })
      );
      data.teachers = creatorArray as any;
    }
    let images: ImageArrayType[] = [];
    if (needClubImage) {
      await Promise.all(
        data.imagesKeysAndIds.map(async (m) => {
          const clubImage = await awsDownload(m.key);
          const imgUrl = clubImage?.data ?? "";
          images.push({
            key: m.key,
            id: m.id,
            url: imgUrl,
          });
        })
      );
    }
    return {
      ...data,
      images,
    };
  } catch (error) {
    console.error(`Something went wrong in getAllClubDetails due to `, error);
    throw error;
  }
};

type createPriceInStripeProps = {
  price: number;
  productId: string;
  currency: string;
};
export const createClubPriceInStripe = ({
  price,
  productId,
  currency,
}: createPriceInStripeProps) => {
  return stripe.prices
    .create({
      unit_amount: +price * AMOUNT_CONVERTOR,
      currency,
      recurring: { interval: "week" },
      product: productId,
    })
    .catch((err) => {
      console.error("Stripe price creation error:", err);
      throw err;
    });
};

type getProductIdOfTheClubProps = {
  club: ClubType;
};
const getProductIdOfTheClub = async ({ club }: getProductIdOfTheClubProps) => {
  try {
    return await stripe.products
      .create({
        name: club.title,
        description: club.about,
        id: club._id,
      })
      .catch((err) => {
        console.error("Stripe product creation error:", err);
        throw err;
      });
  } catch (error) {
    console.error(
      "Something went wrong in getProductIdOfTheClub due to ",
      error
    );
    throw error;
  }
};

type handleClubPricesInfoAndUpdateProps = {
  club: ClubType;
  price: number;
  existingPayloadToUpdate: object;
  currency: string;
};
export const handleClubPricesInfoAndUpdate = async ({
  club,
  price,
  currency,
  existingPayloadToUpdate = {},
}: handleClubPricesInfoAndUpdateProps) => {
  try {
    let clubDetails = club;
    let productId = clubDetails.stripeProductId;
    console.log("productId", productId);
    if (!productId) {
      const productDetails = await getProductIdOfTheClub({ club });
      console.log("productDetails", productDetails);
      productId = productDetails.id;
    }

    const newStripePriceId = await createClubPriceInStripe({
      price,
      productId,
      currency,
    });
    const newHistoricalPrice = await ClubPriceDetails.create({
      priceId: newStripePriceId.id,
      amount: price,
      currency,
      clubId: clubDetails._id,
    });
    console.log({
      newStripePriceId,
      newHistoricalPrice,
      productId,
    });
    const updatedClub = await Club.findByIdAndUpdate(
      clubDetails._id,
      {
        ...existingPayloadToUpdate,
        stripePriceId: newStripePriceId.id,
        stripeProductId: productId,
        $push: { historicalPrices: newHistoricalPrice._id },
      },
      { new: true }
    );
    return updatedClub;
  } catch (error) {
    console.error(
      "Something went wrong in handleClubPricesInfoAndUpdate due to ",
      error
    );
    throw error;
  }
};

type createClubPriceAndProductProps = {
  clubDetails: ClubType;
};

export const createClubPriceAndProduct = async ({
  clubDetails,
}: createClubPriceAndProductProps) => {
  const club = clubDetails;
  const clubId = club._id.toString();
  const currency =
    clubDetails?.currency?.toLowerCase() ?? CURRENCY_ENUM.USD.toLowerCase();

  if (club?.stripeProductId && club?.stripePriceId) {
    return club;
  }

  const updateClubStripeDetails = async (
    productId: string,
    priceId: string
  ) => {
    return Club.findByIdAndUpdate(
      club._id,
      { stripeProductId: productId, stripePriceId: priceId },
      { new: true }
    ).catch((err) => {
      console.error("MongoDB update error:", err);
      throw err;
    });
  };

  try {
    let product = await stripe.products.retrieve(clubId).catch((err) => {
      if (err?.code === "resource_missing") return null;
      throw err;
    });

    if (!product) {
      product = await stripe.products
        .create({
          name: club.title,
          description: club.about,
          id: clubId,
        })
        .catch((err) => {
          console.error("Stripe product creation error:", err);
          throw err;
        });
    }

    const prices = await stripe.prices
      .list({ product: product.id, limit: 1 })
      .catch((err) => {
        console.error("Stripe price listing error:", err);
        throw err;
      });

    const getPriceDetails = async () => {
      if (prices.data.length > 0) {
        return prices.data[0] as any;
      }
      const info = await createClubPriceInStripe({
        price: +club.price,
        productId: product.id,
        currency,
      });
      return info;
    };
    const price = await getPriceDetails();
    return updateClubStripeDetails(product.id, price.id);
  } catch (error) {
    console.error("createClubPriceAndProduct error:", error);
    return null;
  }
};

const getUtcDayBoundaries = (date: Date, timezone: string) => {
  const dateInUserTZ = utcToZonedTime(date, timezone);
  const startOfUserDay = startOfDay(dateInUserTZ);
  const startOfNextUserDay = addDays(startOfUserDay, 1);
  const startOfDayUTC = zonedTimeToUtc(startOfUserDay, timezone);
  const startOfNextDayUTC = zonedTimeToUtc(startOfNextUserDay, timezone);
  return { startOfDayUTC, startOfNextDayUTC };
};

type DateQueryProps = {
  type: CLASSESS_FETCH_DURATION;
  date: string;
  timezone: string;
  currentDate: Date;
  from: Maybe<String>;
  to: Maybe<String>;
};

const generateDateQuery = (
  props: DateQueryProps,
  fieldNames: { startField: string; endField: string }
) => {
  const { type, date, currentDate, timezone, from, to } = props;
  const { startField, endField } = fieldNames;

  const overlapQuery = (start: Date, end: Date) => ({
    $or: [
      { [startField]: { $gte: start, $lt: end } },
      { [endField]: { $gte: start, $lt: end } },
      { [startField]: { $lte: start }, [endField]: { $gte: end } },
    ],
  });

  if (from && to) {
    const { startOfDayUTC: fromUTC } = getUtcDayBoundaries(
      new Date(String(from)),
      timezone
    );
    const { startOfNextDayUTC: toExclusiveUTC } = getUtcDayBoundaries(
      new Date(String(to)),
      timezone
    );
    return overlapQuery(fromUTC, toExclusiveUTC);
  }

  const baseDate = date && date !== "undefined" ? new Date(date) : currentDate;

  const { startOfDayUTC, startOfNextDayUTC } = getUtcDayBoundaries(
    baseDate,
    timezone
  );

  const currentDayOverlapQuery = overlapQuery(startOfDayUTC, startOfNextDayUTC);

  if (+type === CLASSESS_FETCH_DURATION.PAST) {
    return {
      $or: [
        { [endField]: { $lt: startOfDayUTC } },
        ...currentDayOverlapQuery.$or,
      ],
    };
  }

  if (+type === CLASSESS_FETCH_DURATION.CURRENT) {
    return currentDayOverlapQuery;
  }

  if (+type === CLASSESS_FETCH_DURATION.UPCOMING) {
    return {
      [startField]: { $gte: startOfNextDayUTC },
    };
  }
  return {};
};

export const handleScheduleDateQuery = (props: DateQueryProps) => {
  return generateDateQuery(props, {
    startField: "startDate",
    endField: "endDate",
  });
};

export const handleEventDateQuery = (props: DateQueryProps) => {
  return generateDateQuery(props, {
    startField: "eventOnInfo.startDateTime",
    endField: "eventOnInfo.endDateTime",
  });
};

type handleVideoCollectionMapProps = {
  videoIds: string[];
  collectionId: string;
};
export const handleVideoCollectionMap = async ({
  videoIds,
  collectionId,
}: handleVideoCollectionMapProps) => {
  try {
    const payload = videoIds.map((m) => ({
      videoId: m,
      collectionId,
    }));
    await VideoCollectionMap.insertMany(payload);
  } catch (error) {
    console.error(`Something went wrong in handleCollections due to`, error);
  }
};

type handleCollectionProgressProps = {
  collectionId: string;
  userId: string;
};
export const handleCollectionProgress = async ({
  collectionId,
  userId,
}: handleCollectionProgressProps) => {
  try {
    const videosList = await VideoCollectionMap.find({
      collectionId,
    }).select("videoId");

    const vidProgressList = await VideoProgress.find({
      videoId: { $in: videosList.map((m) => m.videoId) },
      userId,
    });

    const videoDurations = await Video.find({
      _id: { $in: videosList.map((m) => m.videoId) },
    }).select("duration");

    const accumulatedDurations = videoDurations.reduce(
      (acc, m) => acc + +m.duration,
      0
    );

    const isEveryWatched = vidProgressList.every((m) => m.progress === 100);
    const accumulatedProgress = vidProgressList.reduce(
      (acc, m) => acc + +m.progress,
      0
    );

    const videoLength = videosList.length;

    await VideoCollectionProgress.findOneAndUpdate(
      { collectionId, userId },
      {
        userId,
        state: isEveryWatched
          ? PROGRESS_STATUS.COMPLETE
          : PROGRESS_STATUS.IN_PROGRESS,
        collectionId,
        progress: videoLength === 0 ? 0 : accumulatedProgress / videoLength,
      },
      { upsert: true }
    );

    await VideoCollection.findByIdAndUpdate(collectionId, {
      videoCount: videoLength,
      videoDuration: accumulatedDurations,
    });
  } catch (error) {
    console.error(
      `Something went wrong in handleCollectionProgress due to`,
      error
    );
    throw error;
  }
};

type giftClassProps = {
  recipentEmail: string;
  transactionId: string;
  giftedBy: string;
  classInfo: ClassesPricingType;
  plan: localPlanType;
};
export const giftClass = async ({
  recipentEmail,
  transactionId,
  giftedBy,
  classInfo,
  plan,
}: giftClassProps) => {
  try {
    const user = await User.findOne({ email: recipentEmail });
    if (user) {
      const updatedPlan = {
        ...plan,
        isDateEnabled: false,
      };
      if (updatedPlan.startDate) {
        delete updatedPlan.startDate;
      }
      if (updatedPlan.endDate) {
        delete updatedPlan.endDate;
      }
      const createdTransaction = await Transaction.create({
        userId: user._id,
        status: PAYMENT_STATUS.SUCCESS,
        email: user.email,
        isGift: true,
        giftedBy: giftedBy,
        giftTransactionId: transactionId,
        classesDetails: [
          {
            classInfo,
            plans: [updatedPlan],
          },
        ],
      });
      if (!createdTransaction) {
        throw new Error("Failed to create gift transaction");
      }
    }
  } catch (error) {
    console.error(`Something went wrong in giftClass due to`, error);
    throw error;
  }
};

type giftClubMembershipsProps = {
  recipentEmail: string;
  transactionId: string;
  giftedBy: string;
  clubDetails: ClubType;
  membership: SingleMembershipType;
};
export const giftClubMemberships = async ({
  recipentEmail,
  transactionId,
  giftedBy,
  clubDetails,
  membership,
}: giftClubMembershipsProps) => {
  try {
    const user = await User.findOne({ email: recipentEmail });
    if (user) {
      const createdTransaction = await Transaction.create({
        userId: user._id,
        status: PAYMENT_STATUS.SUCCESS,
        email: user.email,
        isGift: true,
        giftedBy: giftedBy,
        giftTransactionId: transactionId,
        clubsDetails: [
          {
            clubInfo: clubDetails,
            memberships: [membership],
          },
        ],
      });
      if (!createdTransaction) {
        throw new Error("Failed to create gift transaction");
      }
    }
  } catch (error) {
    console.error(`Something went wrong in giftClass due to`, error);
    throw error;
  }
};
