import {
  CartType,
  ClassesPricingType,
  ClubType,
  EventSchemaType,
  TransactionType,
  UserType,
} from "@/api/mongoTypes";
import CreatorDetails from "@/components/classes/OnlineClub/CreatorDetails";
import { CLASSES_TYPE, PAYMENT_MODE, PLAN_FOR } from "@/constant/Enums";
import { formatDate, isFutureDate } from "@/utils/dateTime";
import { getPricingTitle, getSubTypeName } from "@/utils/format";
import { Box, Button, SxProps, Theme, Typography } from "@mui/material";
import Image from "next/image";
import React, { useMemo, useState } from "react";
import StartEnd from "./StartEnd";
import PurchasedOn from "./PurchasesOn";
import CardImage from "./CardImage";
import PlanDetails from "./PlanDetails";
import PurchasedFor from "./PurchasedFor";
import {
  getClassTitleAndSubtitle,
  getCreatorDetails,
  getCustomProductInfo,
  getStartAndEndDateOfClasses,
  getTypedClassPricingInfo,
  getTypedClubInfo,
  getTypedEventInfo,
} from "@/utils/classes";
import axiosInstance from "@/utils/interceptor";
import { useSnackbar } from "@/hooks/useSnackbar";
import { purchaseDetailsType } from "@/types";

type PurchaseCardProps = React.FC<{
  purchaseDetails: purchaseDetailsType;
  setPurchasesData?: React.Dispatch<
    React.SetStateAction<purchaseDetailsType[]>
  >;
  isDashboard?: boolean;
  index: number;
  onUnSubscribe?: () => void;
}>;

const PurchaseCard: PurchaseCardProps = ({
  purchaseDetails,
  setPurchasesData,
  isDashboard,
  index,
  onUnSubscribe = () => {},
}) => {
  const [unsubscribing, setUnsubscribing] = useState(false);
  const { showSnackbar } = useSnackbar();

  const boughtAt = useMemo(() => {
    if (purchaseDetails && "createdAt" in purchaseDetails) {
      return purchaseDetails.createdAt;
    }
    return null;
  }, [purchaseDetails]);

  const { title, subTitle } = useMemo(() => {
    const customProductInfo = getCustomProductInfo(
      purchaseDetails?.customProductsDetails?.customProductsInfo
    );
    if (customProductInfo) {
      return {
        title: customProductInfo.title,
        subTitle: "Custom Product",
      };
    }
    return getClassTitleAndSubtitle({ cartData: purchaseDetails });
  }, [purchaseDetails]);

  const creatorDetails = useMemo(() => {
    return getCreatorDetails({ cartData: purchaseDetails });
  }, [purchaseDetails]);

  const { email, isForSomeone } = useMemo(() => {
    const defaultValues = {
      email: "",
      isForSomeone: false,
    };
    if (!purchaseDetails) {
      return defaultValues;
    }

    const currentPlanDetails = purchaseDetails?.classesDetails?.plans;
    const currentMemberships = purchaseDetails?.clubsDetails?.memberships;

    if (
      currentPlanDetails?.planFor &&
      purchaseDetails?.classesDetails?.classInfo?.plans?.length > 0
    ) {
      defaultValues.isForSomeone =
        currentPlanDetails?.planFor === PLAN_FOR.SOMEONE;
      defaultValues.email = currentPlanDetails?.emailId ?? "";
      return defaultValues;
    }

    if (
      currentMemberships?.planFor &&
      purchaseDetails?.clubsDetails?.clubInfo?._id
    ) {
      defaultValues.isForSomeone =
        currentMemberships?.planFor === PLAN_FOR.SOMEONE;
      defaultValues.email = currentMemberships?.emailId ?? "";
      return defaultValues;
    }

    return defaultValues;
  }, [purchaseDetails]);

  const showActivateClassButton = useMemo(() => {
    if (!purchaseDetails) {
      return false;
    }
    const clubDetails = getTypedClubInfo(purchaseDetails);
    const eventDetails = getTypedEventInfo(purchaseDetails);
    if (eventDetails && eventDetails._id) {
      return false;
    }
    if (clubDetails && clubDetails._id) {
      return false;
    }
    const currentPlanDetails = purchaseDetails?.classesDetails?.plans;
    if (currentPlanDetails?.planFor === PLAN_FOR.MYSELF) {
      const { startDate, isDateEnabled } = currentPlanDetails;
      return !isDateEnabled;
      // return !isFutureDate(startDate);
    }
    return false;
  }, [purchaseDetails]);

  const {
    isSubscription,
    stripeSubscriptionId,
    subscriptionEndDate,
    isUnsubscribed,
  } = useMemo(() => {
    let defaultValues = {
      isSubscription: false,
      stripeSubscriptionId: null,
      subscriptionEndDate: null,
      isUnsubscribed: false,
    };
    if (!purchaseDetails) {
      return defaultValues;
    }
    const isSubscription = purchaseDetails?.mode === PAYMENT_MODE.SUBSCRIPTION;

    defaultValues.isSubscription = isSubscription;
    defaultValues.subscriptionEndDate = purchaseDetails?.subscriptionEndDate;
    defaultValues.stripeSubscriptionId = purchaseDetails?.stripeSubscriptionId;
    defaultValues.isUnsubscribed = purchaseDetails?.unsubscribe?.value
      ? Boolean(purchaseDetails?.unsubscribe?.value)
      : false;
    return defaultValues;
  }, [purchaseDetails]);

  const handleUnsubscribe = async () => {
    try {
      setUnsubscribing(true);
      const { data } = await axiosInstance.post(`payment/cancel-club-sub`, {
        transactionId: purchaseDetails._id,
      });
      console.log("data", data);
      if (data.success) {
        showSnackbar("Unsubscribed club successfully", {
          type: "success",
        });
        setPurchasesData((prev) => {
          const newList = prev.map((m) => {
            if (m._id === purchaseDetails._id) {
              return {
                ...m,
                // transactionId: data.data,
                unsubscribe: data.data.unsubscribe,
                subscriptionEndDate: data.data.subscriptionEndDate,
                invoices: data.data.invoices,
                transactionDate: data.data.transactionDate,
              };
            } else {
              return m;
            }
          });
          return newList as purchaseDetailsType[];
        });
        onUnSubscribe && onUnSubscribe();
      } else {
        showSnackbar("Something went wrong while unsubscribing club", {
          type: "error",
        });
      }
      setUnsubscribing(false);
    } catch (error) {
      setUnsubscribing(false);
    }
  };

  const { giftedByEmail, isGifted } = useMemo(() => {
    if (purchaseDetails) {
      const giftedUserDetails = (() => {
        const user = purchaseDetails.giftedBy;
        if (user && "_id" in user) {
          return user as UserType;
        }
        return null;
      })();

      return {
        isGifted: purchaseDetails.isGift,
        giftedByEmail: giftedUserDetails?.email,
      };
    }
    return {
      isGifted: false,
      giftedByEmail: null,
    };
  }, [purchaseDetails]);

  return (
    <Box
      sx={{
        width: "100%",
        boxShadow: "0px 1px 10px 0px rgba(0, 0, 0, 0.1)",
        borderRadius: 2,
        mb: 2,
        p: 3,
        minHeight: 205,
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
      }}
    >
      <Box display="flex" flexDirection="row" alignItems="center" gap={2}>
        <CardImage purchaseDetails={purchaseDetails} />

        <Box>
          <Typography fontWeight={700} fontSize={18} mb={0}>
            {title}
          </Typography>
          <Typography fontSize={13} color="rgba(109, 109, 109, 1)">
            {subTitle}
          </Typography>
        </Box>
      </Box>
      {creatorDetails?.length > 0 && (
        <CreatorDetails creatorDetails={creatorDetails} />
      )}
      <PlanDetails purchaseDetails={purchaseDetails} />
      {/* <Progress /> */}
      {boughtAt && (
        <PurchasedOn
          text={isSubscription ? "Subscribed On" : "Purchased On"}
          date={boughtAt}
        />
      )}
      {isForSomeone && (
        <PurchasedFor giftedBy={giftedByEmail} email={email} />
      )}
      <StartEnd
        showActivateClassButton={showActivateClassButton}
        purchaseDetails={purchaseDetails}
        isForSomeone={isForSomeone}
        setPurchasesData={setPurchasesData}
        index={index}
      />
      {isSubscription && (
        <>
          <Typography
            textAlign="end"
            fontSize={12}
            color="rgba(109, 109, 109, 1)"
          >
            Billed weekly
          </Typography>
          <Button
            disabled={unsubscribing}
            onClick={() => {
              if (isUnsubscribed) {
                showSnackbar("Club is already unsubscribed", {
                  type: "error",
                });
              } else {
                handleUnsubscribe();
              }
            }}
            sx={{
              width: "100%",
              fontSize: "0.8rem",
              background: "rgba(20, 167, 156, 1)",
            }}
          >
            {isUnsubscribed ? "Unsubscribed" : "Unsubscribe"}
          </Button>
        </>
      )}
    </Box>
  );
};

export default PurchaseCard;
