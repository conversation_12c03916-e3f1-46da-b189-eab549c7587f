import useTranslate from "@/hooks/useTranslate";
import { Box, Typography } from "@mui/material";
import React from "react";

type PurchasedForProps = React.FC<{
  email: string;
  giftedBy: string;
}>;
const PurchasedFor: PurchasedForProps = ({ email, giftedBy }) => {
  const { translate } = useTranslate();
  return (
    <Box display="flex" flexDirection="row">
      <Typography fontWeight={700} fontSize={12}>
        {translate(giftedBy ? "dash.gifted-by" : "dash.purchased-for")}&nbsp;
      </Typography>
      &nbsp;
      <Typography fontSize={12} color="rgba(100, 116, 139, 1)">
        {giftedBy ?? email}
      </Typography>
    </Box>
  );
};

export default PurchasedFor;
