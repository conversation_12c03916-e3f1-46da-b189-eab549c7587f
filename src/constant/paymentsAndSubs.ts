enum PaymentAndSubEnum {
  IN_PERSON_CLASSES = "IN_PERSON_CLASSES",
  ONLINE_CLASSES = "ONLINE_CLASSES",
  ONLINE_CLUBS = "ONLINE_CLUBS",
  COMMUNITY_EXPERIENCES = "COMMUNITY_EXPERIENCES",
  CUSTOM_PRODUCTS = "CUSTOM_PRODUCTS",
}

const paymentAndSubsType = [
  {
    id: 1,
    name: "In Person Classes",
    value: PaymentAndSubEnum.IN_PERSON_CLASSES,
  },
  {
    id: 2,
    name: "Online Classes",
    value: PaymentAndSubEnum.ONLINE_CLASSES,
  },
  {
    id: 3,
    name: "Online Clubs",
    value: PaymentAndSubEnum.ONLINE_CLUBS,
  },
  {
    id: 4,
    name: "Community Experiences",
    value: PaymentAndSubEnum.COMMUNITY_EXPERIENCES,
  },
  {
    id: 5,
    name: "Custom Products",
    value: PaymentAndSubEnum.CUSTOM_PRODUCTS,
  },
];

export { paymentAndSubsType, PaymentAndSubEnum };
