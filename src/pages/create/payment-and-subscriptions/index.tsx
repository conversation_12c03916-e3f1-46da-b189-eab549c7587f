import CreateHeader from "@/components/Create/CreateHeader";
import { CURRENCY_ENUM } from "@/constant/Enums";
import { paymentAndSubsType } from "@/constant/paymentsAndSubs";
import { useSnackbar } from "@/hooks/useSnackbar";
import axiosInstance from "@/utils/interceptor";
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import Head from "next/head";
import React, { useEffect, useState } from "react";

const containerStyles = {
  display: "flex",
  flexDirection: "column",
  gap: "5",
  marginTop: "20px",
  maxWidth: "80%",
  alignItems: "center",
  width: "100%",
  margin: "0 auto",
  padding: { xs: "20px", sm: "0px" },
};

const LIMIT = 6;

const PaymentsAndSubscriptions = () => {
  const [search, setSearch] = useState("");
  const [contentType, setContentType] = useState("");
  const [currency, setCurrency] = useState("");
  const [skip, setSkip] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [hasMore, setHasMore] = useState(false);
  const [data, setData] = useState([]);

  const { showSnackbar } = useSnackbar();

  const fetchData = async ({ skipCount = 0 }) => {
    const handleError = () => {
      showSnackbar("Failed to fetch data", {
        type: "error",
      });
      setIsLoading(false);
    };

    const showLoading = +skipCount === 0;

    try {
      if (showLoading) {
        setIsLoading(false);
        setData([]);
      }
      const { data: respData } = await axiosInstance.get(
        "payment/purchases/all",
        {
          params: {
            skip: skip,
            limit: LIMIT,
            contentType,
          },
        }
      );
      console.log("respData", respData);
    } catch (error) {
      console.error("Something went wrong in fetchData due to ", error);
      handleError();
    }
  };

  useEffect(() => {
    fetchData({ skipCount: 0 });
  }, []);

  return (
    <>
      <Head>
        <title>Payments And Subscriptions</title>
      </Head>
      <Box sx={containerStyles}>
        <CreateHeader
          text="Payments & Subscriptions"
          maxWidth="100%"
          bottomBorderNeeded
        />
        <Box
          display="flex"
          flexDirection="row"
          alignItems="center"
          mt={4}
          gap={2}
          width="100%"
          justifyContent="start"
          sx={{
            position: "sticky",
            top: 80,
            background: "#fff",
          }}
        >
          <TextField
            label="Search"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
          />
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Filter by Type</InputLabel>
            <Select
              value={contentType}
              label="Filter by Type"
              onChange={(e) => {
                const newRole = e.target.value;
                setContentType(newRole);
              }}
            >
              <MenuItem value="">All Type</MenuItem>
              {paymentAndSubsType.map((m) => (
                <MenuItem key={m.id} value={m.value}>
                  {m.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          {/* <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Filter by Currency</InputLabel>
            <Select
              value={currency}
              label="Filter by Currency"
              onChange={(e) => {
                const newRole = e.target.value;
                setCurrency(newRole);
              }}
            >
              <MenuItem value="">All</MenuItem>
              {Object.keys(CURRENCY_ENUM).map((m) => (
                <MenuItem key={m} value={m}>
                  {m}
                </MenuItem>
              ))}
            </Select>
          </FormControl> */}
        </Box>
      </Box>
    </>
  );
};

export default PaymentsAndSubscriptions;
