import { Grid, Container, Card, Box, Typography } from "@mui/material";
import Link from "next/link";
import { useRouter } from "next/router";
import React from "react";
import Head from "next/head";
const uploadTypes = [
  {
    id: 1,
    name: "Add a Creator",
    href: "/create/creator",
  },
  {
    id: 2,
    name: "Upload Video",
    href: "/create/video",
  },
  {
    id: 3,
    name: "Create Collection",
    href: "/create/collection",
  },
  {
    id: 4,
    name: "Create Event",
    href: "/create/event",
  },
  {
    id: 5,
    name: "Create Club",
    href: "/create/club",
  },
  {
    id: 6,
    name: "Roles Management",
    href: "/create/roles-management",
  },
  {
    id: 7,
    name: "Schedule Class",
    href: "/create/schedule",
  },
  {
    id: 8,
    name: "Student Dashboard",
    href: "/create/student-dashboard",
  },
  {
    id: 9,
    name: "Class Pricing",
    href: "/create/classes-pricing",
  },
  {
    id: 10,
    name: "Custom Product",
    href: "/create/custom-product",
  },
  {
    id: 11,
    name: "Subscriptions & Payments",
    href: "/create/payment-and-subscriptions",
  },
];

const Create = () => {
  const router = useRouter();

  return (
    <>
      <Head>
        <title>Create</title>
      </Head>
      <Container
        sx={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          width: {
            xs: "100%",
            sm: 500,
          },
          gap: 4,
          marginTop: "1%",
          justifyContent: {
            xs: "center",
            sm: "start",
          },
        }}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
            gap: 4,
            width: "100%",
            alignItems: {
              xs: "center",
              sm: "start",
            },
          }}
        >
          {uploadTypes.slice(0, 2).map((m) => (
            <SelectCard
              key={m.id}
              name={m.name}
              index={m.id}
              onClick={() => {
                router.push(m.href);
              }}
            />
          ))}
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
            gap: 4,
            width: "100%",
            alignItems: {
              xs: "center",
              sm: "start",
            },
          }}
        >
          {uploadTypes.slice(2, 4).map((m) => (
            <SelectCard
              key={m.id}
              name={m.name}
              index={m.id}
              onClick={() => {
                router.push(m.href);
              }}
            />
          ))}
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
            gap: 4,
            width: "100%",
            alignItems: {
              xs: "center",
              sm: "start",
            },
          }}
        >
          {uploadTypes.slice(4, 6).map((m) => (
            <SelectCard
              key={m.id}
              name={m.name}
              index={m.id}
              onClick={() => {
                router.push(m.href);
              }}
            />
          ))}
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
            gap: 4,
            width: "100%",
            alignItems: {
              xs: "center",
              sm: "start",
            },
          }}
        >
          {uploadTypes.slice(6, 8).map((m) => (
            <SelectCard
              key={m.id}
              name={m.name}
              index={m.id}
              onClick={() => {
                router.push(m.href);
              }}
            />
          ))}
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
            gap: 4,
            width: "100%",
            alignItems: {
              xs: "center",
              sm: "start",
            },
          }}
        >
          {uploadTypes.slice(8, 10).map((m) => (
            <SelectCard
              key={m.id}
              name={m.name}
              index={m.id}
              onClick={() => {
                router.push(m.href);
              }}
            />
          ))}
        </Box>
        <Box
          sx={{
            display: "flex",
            flexDirection: {
              xs: "column",
              sm: "row",
            },
            gap: 4,
            width: "100%",
            alignItems: {
              xs: "center",
              sm: "start",
            },
          }}
        >
          {uploadTypes.slice(10, 12).map((m) => (
            <SelectCard
              key={m.id}
              name={m.name}
              index={m.id}
              onClick={() => {
                router.push(m.href);
              }}
            />
          ))}
        </Box>
      </Container>
    </>
  );
};

const SelectCard = ({ index, onClick, name }) => {
  return (
    <Card
      key={index}
      sx={{
        height: 150,
        width: 180,
        borderRadius: 5,
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
        background: "rgba(250, 193, 96, 1)",
        cursor: "pointer",
      }}
      onClick={onClick}
    >
      <Icon id={index} />
      <Typography
        sx={{
          fontSize: "1rem",
          textWrap: "wrap",
          marginTop: 5,
          px: 10,
          textAlign: "center",
          fontWeight: 600,
        }}
      >
        {name}
      </Typography>
    </Card>
  );
};

const Icon = ({ id }) => {
  if (id === 1) {
    return (
      <svg
        width="25"
        height="26"
        viewBox="0 0 25 26"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M16.841 2.15785C15.7388 0.967832 14.1993 0.3125 12.5001 0.3125C10.7918 0.3125 9.24721 0.963867 8.15008 2.14652C7.04106 3.34221 6.50071 4.96723 6.62758 6.72195C6.87906 10.1838 9.51342 13 12.5001 13C15.4867 13 18.1166 10.1844 18.372 6.72309C18.5006 4.98422 17.9568 3.3626 16.841 2.15785ZM22.4688 25.6875H2.53133C2.27037 25.6909 2.01193 25.6361 1.77482 25.527C1.53771 25.418 1.32789 25.2574 1.16063 25.0571C0.792463 24.617 0.644064 24.016 0.753947 23.4083C1.23199 20.7564 2.72391 18.5287 5.06883 16.9648C7.15207 15.5766 9.79096 14.8125 12.5001 14.8125C15.2092 14.8125 17.8481 15.5771 19.9313 16.9648C22.2763 18.5281 23.7682 20.7558 24.2462 23.4077C24.3561 24.0155 24.2077 24.6164 23.8395 25.0565C23.6723 25.257 23.4625 25.4176 23.2254 25.5268C22.9883 25.6359 22.7298 25.6908 22.4688 25.6875Z"
          fill="black"
        />
      </svg>
    );
  }
  if (id === 9) {
    return (
      <svg
        width="25"
        height="26"
        viewBox="0 0 42 43"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M36.7499 15.5773V5.4875C36.7514 5.00509 36.6576 4.52713 36.4739 4.08106C36.2902 3.63499 36.0203 3.22959 35.6795 2.88812C35.3387 2.54666 34.9338 2.27587 34.4881 2.09129C34.0424 1.9067 33.5647 1.81196 33.0823 1.8125H22.9982C22.3369 1.81386 21.7029 2.0759 21.2337 2.54176L1.07366 22.696C0.38609 23.3858 0 24.32 0 25.2939C0 26.2679 0.38609 27.2021 1.07366 27.8919L10.6713 37.4895C11.3609 38.1773 12.295 38.5635 13.2688 38.5635C14.2427 38.5635 15.1768 38.1773 15.8664 37.4895L36.0206 17.3418C36.4866 16.8727 36.7487 16.2386 36.7499 15.5773ZM28.8749 12.3125C28.3557 12.3125 27.8482 12.1585 27.4165 11.8701C26.9848 11.5817 26.6484 11.1717 26.4497 10.692C26.251 10.2124 26.199 9.68459 26.3003 9.17539C26.4016 8.66619 26.6516 8.19846 27.0187 7.83135C27.3858 7.46423 27.8536 7.21423 28.3628 7.11294C28.872 7.01165 29.3998 7.06364 29.8794 7.26232C30.3591 7.461 30.769 7.79745 31.0575 8.22913C31.3459 8.66081 31.4999 9.16833 31.4999 9.6875C31.4999 10.3837 31.2233 11.0514 30.731 11.5437C30.2387 12.0359 29.5711 12.3125 28.8749 12.3125Z"
          fill="black"
        />
        <path
          d="M40.6873 5.75C40.3392 5.75 40.0054 5.88828 39.7593 6.13442C39.5131 6.38056 39.3748 6.7144 39.3748 7.0625V17.5108L17.9392 38.9472C17.8122 39.0679 17.7107 39.2128 17.6405 39.3733C17.5704 39.5338 17.5331 39.7068 17.5309 39.882C17.5286 40.0571 17.5615 40.231 17.6275 40.3933C17.6935 40.5555 17.7913 40.703 17.9152 40.8268C18.0391 40.9507 18.1865 41.0485 18.3488 41.1145C18.511 41.1805 18.6849 41.2134 18.8601 41.2111C19.0352 41.2089 19.2082 41.1716 19.3687 41.1015C19.5293 41.0313 19.6741 40.9298 19.7948 40.8028L41.287 19.3106C41.5156 19.0809 41.6963 18.8081 41.8187 18.508C41.9411 18.2079 42.0026 17.8866 41.9998 17.5625V7.0625C41.9998 6.7144 41.8616 6.38056 41.6154 6.13442C41.3693 5.88828 41.0354 5.75 40.6873 5.75Z"
          fill="black"
        />
      </svg>
    );
  }
  if (id === 2) {
    return (
      <svg
        width="25"
        height="24"
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M0.5 1.324C0.5 0.593333 1.09333 0 1.824 0H23.176C23.9067 0 24.5 0.593333 24.5 1.324V22.676C24.4996 23.027 24.36 23.3636 24.1118 23.6118C23.8636 23.86 23.527 23.9996 23.176 24H1.824C1.47285 24 1.13609 23.8605 0.887791 23.6122C0.639493 23.3639 0.5 23.0271 0.5 22.676V1.324ZM10.6627 7.22C10.5824 7.16646 10.4892 7.13569 10.3928 7.13096C10.2965 7.12623 10.2006 7.14772 10.1155 7.19315C10.0304 7.23857 9.95924 7.30623 9.90954 7.38891C9.85985 7.47159 9.83351 7.5662 9.83333 7.66267V16.3373C9.83351 16.4338 9.85985 16.5284 9.90954 16.6111C9.95924 16.6938 10.0304 16.7614 10.1155 16.8069C10.2006 16.8523 10.2965 16.8738 10.3928 16.869C10.4892 16.8643 10.5824 16.8335 10.6627 16.78L17.168 12.444C17.2412 12.3953 17.3011 12.3293 17.3426 12.2518C17.3841 12.1744 17.4059 12.0879 17.4059 12C17.4059 11.9121 17.3841 11.8256 17.3426 11.7482C17.3011 11.6707 17.2412 11.6047 17.168 11.556L10.6627 7.22Z"
          fill="black"
        />
      </svg>
    );
  }
  if (id === 8) {
    return (
      <svg
        width="33"
        height="32"
        viewBox="0 0 33 32"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M27.1667 22.6666C28.6333 22.6666 29.8333 21.4666 29.8333 20V5.33329C29.8333 3.86663 28.6333 2.66663 27.1667 2.66663H13.1667C13.5667 3.46663 13.8333 4.39996 13.8333 5.33329H27.1667V20H15.1667V22.6666M20.5 9.33329V12H12.5V29.3333H9.83333V21.3333H7.16667V29.3333H4.5V18.6666H2.5V12C2.5 10.5333 3.7 9.33329 5.16667 9.33329H20.5ZM11.1667 5.33329C11.1667 6.79996 9.96667 7.99996 8.5 7.99996C7.03333 7.99996 5.83333 6.79996 5.83333 5.33329C5.83333 3.86663 7.03333 2.66663 8.5 2.66663C9.96667 2.66663 11.1667 3.86663 11.1667 5.33329ZM23.1667 7.99996H25.8333V18.6666H23.1667V7.99996ZM19.1667 13.3333H21.8333V18.6666H19.1667V13.3333ZM15.1667 13.3333H17.8333V18.6666H15.1667V13.3333Z"
          fill="black"
        />
      </svg>
    );
  }
  return (
    <svg
      width="33"
      height="32"
      viewBox="0 0 33 32"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_3633_9526)">
        <path
          d="M0.5 26C0.5 26.7956 0.81607 27.5587 1.37868 28.1213C1.94129 28.6839 2.70435 29 3.5 29H29.5C30.2956 29 31.0587 28.6839 31.6213 28.1213C32.1839 27.5587 32.5 26.7956 32.5 26V12C32.5 11.2044 32.1839 10.4413 31.6213 9.87868C31.0587 9.31607 30.2956 9 29.5 9H3.5C2.70435 9 1.94129 9.31607 1.37868 9.87868C0.81607 10.4413 0.5 11.2044 0.5 12L0.5 26ZM4.5 6C4.5 6.26522 4.60536 6.51957 4.79289 6.70711C4.98043 6.89464 5.23478 7 5.5 7H27.5C27.7652 7 28.0196 6.89464 28.2071 6.70711C28.3946 6.51957 28.5 6.26522 28.5 6C28.5 5.73478 28.3946 5.48043 28.2071 5.29289C28.0196 5.10536 27.7652 5 27.5 5H5.5C5.23478 5 4.98043 5.10536 4.79289 5.29289C4.60536 5.48043 4.5 5.73478 4.5 6ZM8.5 2C8.5 2.26522 8.60536 2.51957 8.79289 2.70711C8.98043 2.89464 9.23478 3 9.5 3H23.5C23.7652 3 24.0196 2.89464 24.2071 2.70711C24.3946 2.51957 24.5 2.26522 24.5 2C24.5 1.73478 24.3946 1.48043 24.2071 1.29289C24.0196 1.10536 23.7652 1 23.5 1H9.5C9.23478 1 8.98043 1.10536 8.79289 1.29289C8.60536 1.48043 8.5 1.73478 8.5 2Z"
          fill="black"
        />
      </g>
      <defs>
        <clipPath id="clip0_3633_9526">
          <rect
            width="32"
            height="32"
            fill="white"
            transform="translate(0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default Create;
