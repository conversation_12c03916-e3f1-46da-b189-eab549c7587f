import { Transaction } from "@/api/mongo";
import { PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import { PaymentAndSubEnum } from "@/constant/paymentsAndSubs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        isAscending = false,
        search,
        contentType,
      } = req.query;

      const contentTypeArray = String(contentType).split(",");
      const isCommunityExperience = contentTypeArray.includes(
        PaymentAndSubEnum.COMMUNITY_EXPERIENCES
      );
      const isClub = contentTypeArray.includes(PaymentAndSubEnum.ONLINE_CLUBS);
      const isInPersonClass = contentTypeArray.includes(
        PaymentAndSubEnum.IN_PERSON_CLASSES
      );
      const isOnlineClass = contentTypeArray.includes(
        PaymentAndSubEnum.ONLINE_CLASSES
      );
      const isCustomProducts = contentTypeArray.includes(
        PaymentAndSubEnum.CUSTOM_PRODUCTS
      );

      // Build content type filter conditions
      const contentTypeFilters: any[] = [];

      if (isCommunityExperience) {
        contentTypeFilters.push({
          eventsPriceDetails: { $exists: true, $not: { $size: 0 } },
        });
      }

      if (isClub) {
        contentTypeFilters.push({
          clubsDetails: { $exists: true, $not: { $size: 0 } },
        });
      }

      if (isInPersonClass || isOnlineClass) {
        const classTypeFilter: any = {
          classesDetails: { $exists: true, $not: { $size: 0 } },
        };

        // If both are selected, include all classes
        if (isInPersonClass && isOnlineClass) {
          contentTypeFilters.push(classTypeFilter);
        } else {
          // Filter by specific class type
          const classType = isInPersonClass ? "IN_PERSON" : "ONLINE";
          contentTypeFilters.push({
            ...classTypeFilter,
            "classesDetails.classInfo.type": classType,
          });
        }
      }

      if (isCustomProducts) {
        contentTypeFilters.push({
          customProductsDetails: { $exists: true, $ne: null },
        });
      }

      const pipeline: any[] = [
        // Match successful transactions that are not gifts
        {
          $match: {
            status: PAYMENT_STATUS.SUCCESS,
            isGift: false,
            // Add content type filtering if any filters are specified
            ...(contentTypeFilters.length > 0 &&
            contentType &&
            contentType !== "undefined"
              ? { $or: contentTypeFilters }
              : {}),
          },
        },
        // Populate user data with only required fields and keep as userId
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "userDetails",
            pipeline: [
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  profileImageKey: 1,
                  profileImageId: 1,
                },
              },
            ],
          },
        },
        // Replace userId with populated user data but keep the field name as userId
        {
          $addFields: {
            userId: { $arrayElemAt: ["$userDetails", 0] },
          },
        },
        // Remove the temporary userDetails field
        {
          $unset: "userDetails",
        },
        // Project only the required fields from transaction
        {
          $project: {
            _id: 1,
            userId: 1,
            transactionDate: 1,
            subscriptionEndDate: 1,
            status: 1,
            mode: 1,
            createdAt: 1,
            clubsDetails: 1,
            classesDetails: 1,
            eventsPriceDetails: 1,
            customProductsDetails: 1,
          },
        },
      ];

      // Add search filter if provided (before unwinding to avoid duplicates)
      if (search) {
        pipeline.push({
          $match: {
            $or: [
              { "userId.firstName": { $regex: search, $options: "i" } },
              { "userId.lastName": { $regex: search, $options: "i" } },
            ],
          },
        });
      }

      // Add sorting before unwinding
      pipeline.push({
        $sort: isAscending === "true" ? { createdAt: 1 } : { createdAt: -1 },
      });

      // Add pagination before unwinding to get correct transaction count
      pipeline.push({ $skip: Number(skip) }, { $limit: Number(limit) });

      // Unwind arrays to create separate documents for each item
      // Unwind clubsDetails
      pipeline.push({
        $unwind: {
          path: "$clubsDetails",
          preserveNullAndEmptyArrays: true,
        },
      });

      // Unwind classesDetails
      pipeline.push({
        $unwind: {
          path: "$classesDetails",
          preserveNullAndEmptyArrays: true,
        },
      });

      // Unwind eventsPriceDetails
      pipeline.push({
        $unwind: {
          path: "$eventsPriceDetails",
          preserveNullAndEmptyArrays: true,
        },
      });

      const boughtList = await Transaction.aggregate(pipeline);

      res.status(200).json({
        data: boughtList,
        message: "Transactions fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in payment/purchases/all`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
