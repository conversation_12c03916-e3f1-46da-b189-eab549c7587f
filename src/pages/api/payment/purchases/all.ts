import { Transaction } from "@/api/mongo";
import { CLASSES_SORT, CLASSES_TYPE, PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import mongoose from "mongoose";
import { getAllClubDetails, getAllEventDetails } from "@/api/mongoHelpers";
import { PaymentAndSubEnum } from "@/constant/paymentsAndSubs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        isAscending = false,
        search,
        contentType,
      } = req.query;

      const contentTypeArray = String(contentType).split(",");
      const isCommunityExperience = contentTypeArray.includes(PaymentAndSubEnum.COMMUNITY_EXPERIENCES);
      const isClub = contentTypeArray.includes(PaymentAndSubEnum.ONLINE_CLUBS);
      const isInPersonClass = contentTypeArray.includes(PaymentAndSubEnum.IN_PERSON_CLASSES);
      const isOnlineClass = contentTypeArray.includes(PaymentAndSubEnum.ONLINE_CLASSES);
      const isCustomProducts = contentTypeArray.includes(PaymentAndSubEnum.CUSTOM_PRODUCTS);

      const boughtList = await Transaction.find({
        status: PAYMENT_STATUS.SUCCESS,
        isGift: false,
      });

      res.status(200).json({
        data: boughtList,
        message: "Transactions fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in payment/purchases/all`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
