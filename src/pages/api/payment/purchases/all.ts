import { Transaction } from "@/api/mongo";
import { PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import { PaymentAndSubEnum } from "@/constant/paymentsAndSubs";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        isAscending = false,
        search,
        contentType,
      } = req.query;

      const contentTypeArray = String(contentType).split(",");
      const isCommunityExperience = contentTypeArray.includes(
        PaymentAndSubEnum.COMMUNITY_EXPERIENCES
      );
      const isClub = contentTypeArray.includes(PaymentAndSubEnum.ONLINE_CLUBS);
      const isInPersonClass = contentTypeArray.includes(
        PaymentAndSubEnum.IN_PERSON_CLASSES
      );
      const isOnlineClass = contentTypeArray.includes(
        PaymentAndSubEnum.ONLINE_CLASSES
      );
      const isCustomProducts = contentTypeArray.includes(
        PaymentAndSubEnum.CUSTOM_PRODUCTS
      );

      // Build content type filter conditions
      const contentTypeFilters: any[] = [];

      if (isCommunityExperience) {
        contentTypeFilters.push({
          eventsPriceDetails: { $exists: true, $not: { $size: 0 } },
        });
      }

      if (isClub) {
        contentTypeFilters.push({
          clubsDetails: { $exists: true, $not: { $size: 0 } },
        });
      }

      if (isInPersonClass || isOnlineClass) {
        const classTypeFilter: any = {
          classesDetails: { $exists: true, $not: { $size: 0 } },
        };

        // If both are selected, include all classes
        if (isInPersonClass && isOnlineClass) {
          contentTypeFilters.push(classTypeFilter);
        } else {
          // Filter by specific class type
          const classType = isInPersonClass ? "IN_PERSON" : "ONLINE";
          contentTypeFilters.push({
            ...classTypeFilter,
            "classesDetails.classInfo.type": classType,
          });
        }
      }

      if (isCustomProducts) {
        contentTypeFilters.push({
          customProductsDetails: { $exists: true, $ne: null },
        });
      }

      const pipeline: any[] = [
        // Match successful transactions that are not gifts
        {
          $match: {
            status: PAYMENT_STATUS.SUCCESS,
            isGift: false,
            // Add content type filtering if any filters are specified
            ...(contentTypeFilters.length > 0 &&
            contentType &&
            contentType !== "undefined"
              ? { $or: contentTypeFilters }
              : {}),
          },
        },
        // Populate user data with only required fields
        {
          $lookup: {
            from: "users",
            localField: "userId",
            foreignField: "_id",
            as: "user",
            pipeline: [
              {
                $project: {
                  _id: 1,
                  firstName: 1,
                  lastName: 1,
                  profileImageKey: 1,
                  profileImageId: 1,
                },
              },
            ],
          },
        },
        // Unwind user array to make it an object
        {
          $unwind: {
            path: "$user",
            preserveNullAndEmptyArrays: true,
          },
        },
      ];

      // Add search filter if provided
      if (search) {
        pipeline.push({
          $match: {
            $or: [
              { "user.firstName": { $regex: search, $options: "i" } },
              { "user.lastName": { $regex: search, $options: "i" } },
            ],
          },
        });
      }

      // Add sorting
      pipeline.push({
        $sort: isAscending === "true" ? { createdAt: 1 } : { createdAt: -1 },
      });

      // Add pagination
      pipeline.push({ $skip: Number(skip) }, { $limit: Number(limit) });

      const boughtList = await Transaction.aggregate(pipeline);

      res.status(200).json({
        data: boughtList,
        message: "Transactions fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(`Something went wrong in payment/purchases/all`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
