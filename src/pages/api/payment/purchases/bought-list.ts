import { Transaction } from "@/api/mongo";
import { CLASSES_SORT, CLASSES_TYPE, PAYMENT_STATUS } from "@/constant/Enums";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";
import mongoose from "mongoose";
import { getAllClubDetails, getAllEventDetails } from "@/api/mongoHelpers";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    if (req.method === "GET") {
      const {
        skip = 0,
        limit = 10,
        filters = "",
        isAscending = false,
        search,
      } = req.query;
      const userId = req.headers.userid;

      const filterArray = String(filters).split(",");
      const isCommunityExperience = filterArray.includes(
        CLASSES_SORT.COMMUNITY
      );
      const isClub = filterArray.includes(CLASSES_SORT.ONLINE_CLUB);
      const isInPersonClass = filterArray.includes(
        CLASSES_SORT.IN_PERSON_CLASS
      );
      const isOnlineClass = filterArray.includes(CLASSES_SORT.ONLINE_CLASS);

      const matchConditions: Record<string, any>[] = [
        {
          userId: new mongoose.Types.ObjectId(userId as string),
          status: PAYMENT_STATUS.SUCCESS,
        },
      ];

      const orConditions: any[] = [];

      if (isCommunityExperience) {
        orConditions.push({ eventIds: { $ne: [] } });
      }

      if (isClub) {
        orConditions.push({ clubsDetails: { $ne: [] } });
      }

      if (isInPersonClass) {
        orConditions.push({
          "classesDetails.classInfo.type": CLASSES_TYPE.IN_PERSON,
        });
      }

      if (isOnlineClass) {
        orConditions.push({
          "classesDetails.classInfo.type": CLASSES_TYPE.ONLINE,
        });
      }

      if (orConditions.length > 0) {
        matchConditions.push({ $or: orConditions });
      }

      const pipeline: any[] = [{ $match: { $and: matchConditions } }];

      pipeline.push(
        {
          $unwind: {
            path: "$clubsDetails",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "clubs",
            localField: "clubsDetails.clubInfo",
            foreignField: "_id",
            as: "clubsDetails.clubInfo",
          },
        },
        {
          $unwind: {
            path: "$clubsDetails.clubInfo",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "users",
            localField: "clubsDetails.clubInfo.teachers",
            foreignField: "_id",
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  profileImageKey: 1,
                  profileImageId: 1,
                  _id: 1,
                },
              },
            ],
            as: "clubsDetails.clubInfo.teachers",
          },
        },
        {
          $unwind: {
            path: "$clubsDetails.memberships",
            preserveNullAndEmptyArrays: true,
          },
        }
      );

      // pipeline.push(
      //   {
      //     $unwind: {
      //       path: "$eventIds",
      //       preserveNullAndEmptyArrays: true,
      //     },
      //   },
      //   {
      //     $lookup: {
      //       from: "events",
      //       localField: "eventIds",
      //       foreignField: "_id",
      //       as: "eventIds",
      //     },
      //   },
      //   {
      //     $unwind: {
      //       path: "$eventIds",
      //       preserveNullAndEmptyArrays: true,
      //     },
      //   },
      // );

      pipeline.push(
        {
          $unwind: {
            path: "$eventsPriceDetails",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "events",
            localField: "eventsPriceDetails.eventInfo",
            foreignField: "_id",
            as: "eventsPriceDetails.eventInfo",
          },
        },
        {
          $unwind: {
            path: "$eventsPriceDetails.eventInfo",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: "eventons",
            localField: "eventsPriceDetails.eventOn",
            foreignField: "_id",
            as: "eventsPriceDetails.eventOn",
          },
        },
        {
          $unwind: {
            path: "$eventsPriceDetails.eventOn",
            preserveNullAndEmptyArrays: true,
          },
        }
      );

      pipeline.push(
        {
          $lookup: {
            from: "customproducts",
            localField: "customProductsDetails.customProductsInfo",
            foreignField: "_id",
            as: "customProductsDetails.customProductsInfo",
          },
        },
        {
          $unwind: {
            path: "$customProductsDetails.customProductsInfo",
            preserveNullAndEmptyArrays: true,
          },
        }
      );

      pipeline.push(
        {
          $unwind: {
            path: "$classesDetails",
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $unwind: {
            path: "$classesDetails.plans",
            preserveNullAndEmptyArrays: true,
          },
        }
      );

      pipeline.push({
        $lookup: {
          from: "users",
          localField: "giftedBy",
          foreignField: "_id",
          pipeline: [
            {
              $project: {
                email: 1,
                firstName: 1,
                lastName: 1,
                _id: 1,
              },
            },
          ],
          as: "giftedBy",
        },
      });

      pipeline.push({
        $unwind: {
          path: "$giftedBy",
          preserveNullAndEmptyArrays: true,
        },
      });

      if (search) {
        const regex = new RegExp(search as string, "i");
        pipeline.push({
          $match: {
            $or: [
              { "clubId.title": regex },
              // { "eventId.title": regex },
              { "eventsPriceDetails.eventInfo.title": regex },
              { "classesId.title": regex },
            ],
          },
        });
      }

      pipeline.push(
        { $sort: { createdAt: isAscending === "true" ? 1 : -1 } },
        { $skip: Number(skip) },
        { $limit: Number(limit) }
      );

      const boughtList = await Transaction.aggregate(pipeline);

      const dataWithImages = await Promise.all(
        boughtList.map(async (trans) => {
          // if (trans?.eventIds?._id) {
          //   const expandedEventDetails = await getAllEventDetails({
          //     data: trans?.eventIds,
          //     needEventImage: true,
          //   });
          //   return {
          //     ...trans,
          //     eventIds: expandedEventDetails,
          //   };
          // }
          if (trans?.eventsPriceDetails?.eventInfo?._id) {
            const expandedEventDetails = await getAllEventDetails({
              data: trans.eventsPriceDetails.eventInfo,
              needEventImage: true,
            });
            return {
              ...trans,
              eventsPriceDetails: {
                ...trans.eventsPriceDetails,
                eventInfo: expandedEventDetails,
                // Keep original price + currency from transaction
                price: trans.eventsPriceDetails.price,
                currency: trans.eventsPriceDetails.currency,
                eventOn: trans.eventsPriceDetails.eventOn,
              },
            };
          }
          if (trans?.clubsDetails.clubInfo?._id) {
            const expandedClubDetails = await getAllClubDetails({
              data: trans?.clubsDetails.clubInfo,
              needClubImage: true,
              needCreatorImage: true,
            });
            return {
              ...trans,
              clubsDetails: {
                ...trans.clubsDetails,
                clubInfo: expandedClubDetails,
              },
            };
          }
          return trans;
        })
      );

      res.status(200).json({
        data: dataWithImages,
        message: "Transactions fetched successfully",
        success: true,
      });
    } else {
      res.setHeader("Allow", ["GET"]);
      res.status(405).end("Method Not Allowed");
    }
  } catch (error) {
    console.error(
      `Something went wrong in payment/purchases/bought-list`,
      error
    );
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message,
      success: false,
    });
  }
}
