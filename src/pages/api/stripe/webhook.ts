import {
  getCheckoutSessionIdFromPaymentIntent,
  getReceiptUrl,
} from "@/api/stripe";
import { getMessageAndStatusCode } from "@/utils/responseUtil";
import { NextApiRequest, NextApiResponse } from "next";

const STRIPE_KEY = process.env.STRIPE_KEY;
const stripe = require("stripe")(STRIPE_KEY);
const endpointSecret = process.env.STRIPE_ENDPOINT_KEY;

import { buffer } from "micro";
import { Cart, Transaction, User, Club, CustomProduct } from "@/api/mongo";
import { CLASSES_TYPE, PAYMENT_STATUS, PAYMENT_MODE } from "@/constant/Enums";
import { sendEmail } from "@/api/sendEmail";
import { getPaymentEmailBody } from "@/constant/email/paymentEmail";
import { getCourseGiftBody } from "@/constant/email/courseGiftEmail";
import {
  getPricingTitle,
  isEnglishLanguage,
  nameFromEmail,
} from "@/utils/format";
import { ClubType, TransactionType } from "@/api/mongoTypes";
import {
  getCustomProductInfo,
  getItemsDetailsFromTransaction,
} from "@/utils/classes";
import { handleTranslate } from "@/utils/common";
import { giftClass, giftClubMemberships } from "@/api/mongoHelpers";

export const config = {
  api: {
    bodyParser: false, // Disable automatic body parsing
  },
};

type updateNoOfStudentsEnrolledProps = {
  clubDetails: ClubType;
};
const updateNoOfStudentsEnrolled = async ({
  clubDetails,
}: updateNoOfStudentsEnrolledProps) => {
  try {
    const cluubId = clubDetails?._id;
    if (cluubId) {
      await Club.updateOne(
        { _id: cluubId },
        { $inc: { noOfStudentsEnrolled: 1 } }
      );
    }
  } catch (error) {
    console.error(
      "Something went wrong in updateNoOfStudentsEnrolled in stripe webhook",
      error
    );
  }
};

type sendEmailForThePlansBoughtForOthersProps = {
  username: string;
  userId: string;
  isEnglish: boolean;
  transactionDetail: TransactionType;
};
const sendEmailForThePlansBoughtForOthers = async ({
  username,
  isEnglish,
  userId,
  transactionDetail,
}: sendEmailForThePlansBoughtForOthersProps) => {
  try {
    const transactionId = transactionDetail._id;

    if (transactionDetail?.classesDetails?.length > 0) {
      for (const classDetail of transactionDetail.classesDetails) {
        const plans = classDetail.plans;
        const classDetails = classDetail.classInfo;
        if (plans.length > 0) {
          for (const plan of plans) {
            const recipentEmail = plan.emailId;
            if (recipentEmail) {
              const description = (() => {
                const title = getPricingTitle({
                  data: classDetails,
                });
                const type = (() => {
                  if (classDetails.type === CLASSES_TYPE.COMMUNITY) {
                    return "Community classes";
                  }
                  if (classDetails.type === CLASSES_TYPE.IN_PERSON) {
                    return "In person classes";
                  }
                  if (classDetails.type === CLASSES_TYPE.ONLINE) {
                    return "Online classes";
                  }
                  return "";
                })();
                const shortDescription = isEnglish
                  ? classDetails.description
                  : classDetails.descriptionEs;
                const benefits = isEnglish
                  ? classDetails.benefits
                  : classDetails.benefitsEs;

                return `${title} ${shortDescription}.This ${type} is designed to ${benefits}`;
              })();
              await giftClass({
                recipentEmail,
                transactionId,
                giftedBy: userId,
                classInfo: classDetail.classInfo,
                plan,
              });
              const mailBody = getCourseGiftBody({
                senderName: username,
                userName: nameFromEmail(recipentEmail),
                description: description,
                isEnglish,
              });
              sendEmail({
                body: mailBody,
                subject: `${handleTranslate(
                  "email.ge.special",
                  isEnglish
                )} ${username}!`,
                to: [recipentEmail],
              });
            }
          }
        }
      }
    }

    if (transactionDetail?.clubsDetails?.length > 0) {
      for (const clubDetail of transactionDetail.clubsDetails) {
        const memberships = clubDetail.memberships;
        const clubDetails = clubDetail.clubInfo as ClubType;

        if (memberships.length > 0) {
          updateNoOfStudentsEnrolled({ clubDetails });
          for (const membership of memberships) {
            const recipentEmail = membership.emailId;
            if (recipentEmail) {
              await giftClubMemberships({
                recipentEmail,
                transactionId,
                giftedBy: userId,
                clubDetails,
                membership,
              });
              const mailBody = getCourseGiftBody({
                senderName: username,
                userName: nameFromEmail(recipentEmail),
                description: clubDetails.about,
                isEnglish,
              });
              sendEmail({
                body: mailBody,
                subject: `${handleTranslate(
                  "email.ge.special",
                  isEnglish
                )} ${username}!`,
                to: [recipentEmail],
              });
            }
          }
        }
      }
    }
  } catch (error) {
    console.error(
      "Something went wrong in sendEmailForThePlansBoughtForOthers due to ",
      error
    );
  }
};

type handleTransactionProps = {
  paymentId?: string;
  checkoutId?: string;
  isFailed: Boolean;
  isExpired?: Boolean;
};
const handleTransaction = async ({
  paymentId,
  isFailed = true,
  isExpired = false,
  checkoutId,
}: handleTransactionProps) => {
  console.log({
    paymentId,
    isFailed,
    checkoutId,
    isExpired,
  });
  try {
    if (!checkoutId) {
      checkoutId = await getCheckoutSessionIdFromPaymentIntent(paymentId);
    }
    if (checkoutId) {
      const transactionDetail = await Transaction.findOne({
        stripeCheckoutId: checkoutId,
      })
        .populate("eventIds")
        .populate("customProductsDetails.customProductsInfo")
        .populate([
          {
            path: "clubsDetails",
            populate: {
              path: "clubInfo",
            },
          },
        ]);
      if (!transactionDetail) {
        console.error("No transction with this transctionId found", checkoutId);
        return;
      }
      let stripeSubscriptionId = null;
      let subscriptionEndDate = null;
      if (transactionDetail.mode === PAYMENT_MODE.SUBSCRIPTION) {
        const sessionDetails = await stripe.checkout.sessions.retrieve(
          checkoutId
        );
        if (sessionDetails) {
          stripeSubscriptionId = sessionDetails?.subscription;
          if (
            typeof stripeSubscriptionId === "string" &&
            stripeSubscriptionId
          ) {
            const subscriptionDetails = await stripe.subscriptions.retrieve(
              stripeSubscriptionId
            );
            if (subscriptionDetails) {
              const endDate = new Date(
                subscriptionDetails.current_period_end * 1000
              );
              subscriptionEndDate = endDate;
            }
          }
        }
      }

      const updatingProperties = (() => {
        const list = {
          stripeSubscriptionId,
          subscriptionEndDate,
        };
        if (!stripeSubscriptionId) {
          delete list.stripeSubscriptionId;
        }
        if (!subscriptionEndDate) {
          delete list.subscriptionEndDate;
        }
        return list;
      })();

      await transactionDetail.updateOne({
        status: isExpired
          ? PAYMENT_STATUS.EXPIRED
          : isFailed
          ? PAYMENT_STATUS.FAILED
          : PAYMENT_STATUS.SUCCESS,
        paymentIntentId: paymentId,
        ...updatingProperties,
      });

      const userId = transactionDetail.userId;
      const userDetails = await User.findById(userId).populate(
        "languageOfInterest"
      );

      const isEnglish = isEnglishLanguage(userDetails?.languageOfInterest);

      const username = (() => {
        if (userDetails) {
          const firstName = userDetails.firstName;
          const lastName = userDetails.lastName;
          return `${firstName} ${lastName}`;
        }
        return "";
      })();

      if (!isFailed && !isExpired) {
        const cartIds = transactionDetail.cartId.map((m) => m.toString());
        if (cartIds.length > 0) {
          await sendEmailForThePlansBoughtForOthers({
            username,
            isEnglish,
            userId: userDetails?._id,
            transactionDetail,
          });
          await Cart.updateMany(
            { _id: { $in: cartIds } },
            {
              $set: {
                isCheckedOut: true,
                transactionId: transactionDetail._id,
              },
            }
          );
        } else {
          console.error(
            "Somehow cartIds is empty in transactionDetail which shouldnt be the case",
            transactionDetail
          );
        }

        const customProductInfo = getCustomProductInfo(
          transactionDetail?.customProductsDetails?.customProductsInfo
        );
        if (customProductInfo) {
          await CustomProduct.updateOne(
            { _id: customProductInfo._id },
            {
              isExpired: true,
              transactionId: transactionDetail._id,
              productFor: userId,
            }
          );
        }
      }

      if (!userDetails) {
        console.warn("No user found with this userId", userId);
      } else {
        console.log("transactionDetail", transactionDetail);
        const paymentDetails =
          getItemsDetailsFromTransaction(transactionDetail);
        console.log("paymentDetails", paymentDetails);
        const isSubscription =
          transactionDetail.mode === PAYMENT_MODE.SUBSCRIPTION;

        if (paymentId && !isFailed && !isExpired) {
          const receiptUrl = await getReceiptUrl(paymentId);
          const mailBody = getPaymentEmailBody({
            name: userDetails.firstName,
            receiptUrl,
            isError: false,
            transactionId: transactionDetail._id,
            paymentDetails,
            totalAmount: +transactionDetail.price,
            isSubscription,
            isEnglish,
          });
          sendEmail({
            body: mailBody,
            subject: handleTranslate(
              isSubscription ? "email.club.success" : "email.pe.success",
              isEnglish
            ),
            to: [userDetails.email],
          });
        } else {
          const subject = (() => {
            const id = (() => {
              if (isSubscription) {
                if (isExpired) {
                  return "email.session-expired";
                }
                return "email.sub-failed";
              } else {
                if (isExpired) {
                  return "email.payment-session-expired";
                }
                return "email.payment-failed";
              }
            })();
            return handleTranslate(id, isEnglish);
          })();
          const mailBody = getPaymentEmailBody({
            name: userDetails.firstName,
            isError: true,
            receiptUrl: "",
            transactionId: transactionDetail._id,
            paymentDetails,
            totalAmount: +transactionDetail.price,
            isSubscription,
            isEnglish,
          });
          sendEmail({
            body: mailBody,
            subject: subject,
            to: [userDetails.email],
          });
        }
      }
    } else {
      console.error("No checkoutId found with paymentOId => ", paymentId);
    }
  } catch (error) {
    console.error("Something went wrong in handleTransaction due to ", error);
  }
};

type handleInvoiceProps = {
  invoiceDetails: any;
  isSuccess: boolean;
};
const handleInvoice = async ({
  invoiceDetails,
  isSuccess,
}: handleInvoiceProps) => {
  try {
    const subscription = await stripe.subscriptions.retrieve(
      invoiceDetails.subscription as string
    );

    const transaction = await Transaction.findOne({
      stripeSubscriptionId: invoiceDetails.subscription,
    });

    const payload = {
      subscriptionEndDate: new Date(subscription?.current_period_end * 1000),
      $addToSet: { invoices: invoiceDetails.id },
    };

    if (!isSuccess) {
      delete payload.subscriptionEndDate;
    }

    if (transaction) {
      await Transaction.findByIdAndUpdate(transaction._id, payload);
    }
  } catch (error) {
    console.error("Something went wrong in handleInvoice due to ", error);
  }
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    if (req.method === "POST") {
      const sig = req.headers["stripe-signature"];

      let event;

      try {
        const rawBody = await buffer(req);
        event = stripe.webhooks.constructEvent(rawBody, sig, endpointSecret);
      } catch (err) {
        console.log("err", err);
        res.status(400).send(`Webhook Error: ${err.message}`);
        return;
      }

      const dataObject = event?.data?.object;

      console.log("event.type", event.type);
      console.log("dataObject", dataObject);

      switch (event.type) {
        case "payment_intent.canceled":
          await handleTransaction({
            paymentId: dataObject.id,
            isFailed: true,
          });
          break;

        case "payment_intent.payment_failed":
          await handleTransaction({
            paymentId: dataObject.id,
            isFailed: true,
          });
          break;

        case "checkout.session.expired":
          await handleTransaction({
            checkoutId: dataObject.id,
            paymentId: dataObject.payment_intent,
            isFailed: false,
            isExpired: true,
          });
          break;

        case "payment_intent.succeeded":
          await handleTransaction({
            paymentId: dataObject.id,
            isFailed: false,
          });
          break;

        case "invoice.paid":
          await handleInvoice({
            invoiceDetails: dataObject,
            isSuccess: true,
          });
          break;

        case "invoice.payment_failed":
          await handleInvoice({
            invoiceDetails: dataObject,
            isSuccess: false,
          });
          break;

        default:
          console.log(`Unhandled event type ${event.type}`);
      }

      // Return a 200 response to acknowledge receipt of the event
      res.json({ received: "success" });
    } else {
      res.setHeader("Allow", ["POST"]);
      res.status(405).json({ message: `Method ${req.method} Not Allowed` });
    }
  } catch (error) {
    console.error(`Something went wrong in stripe/webhook due to`, error);
    const { message, status } = getMessageAndStatusCode(error);
    res.status(status).json({
      data: null,
      message: message,
      success: false,
    });
  }
}
